//! # LRA 音频响度范围计算器
//!
//! 这是一个高性能的命令行工具，用于递归计算指定文件夹内所有音频文件的响度范围（Loudness Range, LRA）。
//! 它利用多线程并行处理来最大化效率，并使用业界标准的 FFmpeg 进行核心分析。
//!
//! ## 主要功能
//! - 递归扫描音频文件
//! - 多线程并行处理
//! - 支持多种音频格式
//! - 基于 EBU R128 标准的精确 LRA 计算
//! - 结果自动排序和保存

mod audio;
mod error;
mod processor;
mod utils;

use std::fs::File;
use std::io::{BufWriter, Write};

use chrono::Local;

use audio::{check_ffmpeg_availability, scan_audio_files};
use processor::{analyze_results, display_processing_stats, process_files_parallel};
use utils::{get_folder_path_from_user, sort_lra_results_file};


/// 程序主入口函数
///
/// 执行完整的 LRA 计算流程：
/// 1. 获取用户输入的文件夹路径
/// 2. 递归扫描音频文件
/// 3. 多线程并行分析 LRA 值
/// 4. 保存和排序结果
///
/// # 返回值
/// - `Ok(())` - 程序成功执行完成
/// - `Err(Box<dyn std::error::Error>)` - 执行过程中发生错误
fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("欢迎使用音频 LRA 计算器（高性能版 - 直接分析）！");
    println!("当前时间: {}", Local::now().format("%Y-%m-%d %H:%M:%S"));

    // 检查 FFmpeg 可用性
    if let Err(e) = check_ffmpeg_availability() {
        eprintln!("错误: {e}");
        eprintln!("请安装 FFmpeg 后重试。");
        return Err(e.into());
    }

    let base_folder_path = get_folder_path_from_user()?;
    println!("正在递归扫描文件夹: {}", base_folder_path.display());

    let results_file_path = base_folder_path.join("lra_results.txt");
    let header_line = "文件路径 (相对) - LRA 数值 (LU)";

    // 扫描音频文件
    let files_to_process = scan_audio_files(&base_folder_path, Some(&results_file_path));

    if files_to_process.is_empty() {
        println!("在指定路径下没有找到支持的音频文件。");
        let mut writer = BufWriter::new(File::create(&results_file_path)?);
        writeln!(writer, "{header_line}")?;
        writer.flush()?;
        return Ok(());
    }

    println!(
        "扫描完成，找到 {} 个音频文件待处理。",
        files_to_process.len()
    );

    // 并行处理文件
    let processing_results = process_files_parallel(files_to_process);

    // 分析结果
    let (stats, successful_results) = analyze_results(processing_results);

    // 显示统计信息
    display_processing_stats(&stats);

    // 写入结果文件
    let mut writer = BufWriter::new(File::create(&results_file_path)?);
    writeln!(writer, "{header_line}")?;
    for (path_str, lra) in successful_results {
        writeln!(writer, "{path_str} - {lra:.1}")?;
    }
    writer.flush()?;

    println!("结果写入完成。");

    // 排序结果文件
    if stats.successful > 0 {
        match sort_lra_results_file(&results_file_path, header_line) {
            Ok(_) => println!("结果文件 {} 已成功排序。", results_file_path.display()),
            Err(e) => eprintln!(
                "错误：排序结果文件 {} 失败: {}",
                results_file_path.display(),
                e
            ),
        }
    } else {
        println!("没有成功处理的文件，跳过排序。");
    }

    println!(
        "所有操作完成！结果已保存于: {}",
        results_file_path.display()
    );
    println!("结束时间: {}", Local::now().format("%Y-%m-%d %H:%M:%S"));
    Ok(())
}


