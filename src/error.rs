//! 错误处理模块
//! 
//! 定义了应用程序中使用的各种错误类型和相关的处理逻辑。

use std::fmt;

/// 文件处理错误结构体
/// 
/// 用于封装在处理音频文件过程中发生的错误信息，
/// 包含出错的文件路径和具体的错误描述。
#[derive(Debug)]
pub struct ProcessFileError {
    /// 出错的文件路径
    pub file_path: String,
    /// 错误描述信息
    pub message: String,
}

impl fmt::Display for ProcessFileError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "文件 '{}' 处理失败: {}", self.file_path, self.message)
    }
}

impl std::error::Error for ProcessFileError {}

/// 应用程序的主要错误类型
#[derive(Debug)]
pub enum AppError {
    /// 输入/输出错误
    Io(std::io::Error),
    /// 文件处理错误
    FileProcessing(ProcessFileError),
    /// FFmpeg 相关错误
    Ffmpeg(String),
    /// 路径相关错误
    Path(String),
}

impl fmt::Display for AppError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        match self {
            AppError::Io(err) => write!(f, "输入/输出错误: {err}"),
            AppError::FileProcessing(err) => write!(f, "{err}"),
            AppError::Ffmpeg(msg) => write!(f, "FFmpeg 错误: {msg}"),
            AppError::Path(msg) => write!(f, "路径错误: {msg}"),
        }
    }
}

impl std::error::Error for AppError {
    fn source(&self) -> Option<&(dyn std::error::Error + 'static)> {
        match self {
            AppError::Io(err) => Some(err),
            AppError::FileProcessing(err) => Some(err),
            AppError::Ffmpeg(_) | AppError::Path(_) => None,
        }
    }
}

impl From<std::io::Error> for AppError {
    fn from(err: std::io::Error) -> Self {
        AppError::Io(err)
    }
}

impl From<ProcessFileError> for AppError {
    fn from(err: ProcessFileError) -> Self {
        AppError::FileProcessing(err)
    }
}
