//! 实用工具模块
//! 
//! 包含文件操作、用户输入处理等辅助功能。

use std::fs::File;
use std::io::{self, BufRead, BufReader, BufWriter, Write};
use std::path::{Path, PathBuf};

use crate::error::AppError;

/// 从用户输入获取要处理的文件夹路径
/// 
/// 持续提示用户输入，直到获得一个有效的文件夹路径。
/// 会验证路径是否存在、是否为目录，以及程序是否有访问权限。
/// 
/// # 返回值
/// - `Ok(PathBuf)` - 规范化后的有效文件夹路径
/// - `Err(Box<dyn std::error::Error>)` - 输入/输出错误
pub fn get_folder_path_from_user() -> Result<PathBuf, Box<dyn std::error::Error>> {
    loop {
        print!("请输入要递归处理的音乐顶层文件夹路径: ");
        io::stdout().flush()?;
        let mut input = String::new();
        io::stdin().read_line(&mut input)?;
        let path_str = input.trim();
        if path_str.is_empty() {
            eprintln!("错误: 路径不能为空，请重新输入。");
            continue;
        }
        let path = PathBuf::from(path_str);
        
        // 使用新的验证函数
        if let Err(e) = validate_folder_path(&path) {
            eprintln!("错误: {e}");
            continue;
        }
        
        match path.canonicalize() {
            Ok(canonical_path) => return Ok(canonical_path),
            Err(e) => eprintln!(
                "错误: 无法规范化路径 '{}': {}. 请确保路径有效且程序有权限访问。",
                path.display(),
                e
            ),
        }
    }
}

/// 验证文件夹路径的有效性
/// 
/// 检查路径是否存在、是否为目录、是否有读取权限
pub fn validate_folder_path(path: &Path) -> Result<(), AppError> {
    if !path.exists() {
        return Err(AppError::Path(format!(
            "路径 '{}' 不存在",
            path.display()
        )));
    }
    
    if !path.is_dir() {
        return Err(AppError::Path(format!(
            "路径 '{}' 不是一个目录",
            path.display()
        )));
    }
    
    // 尝试读取目录来检查权限
    match std::fs::read_dir(path) {
        Ok(_) => Ok(()),
        Err(e) => Err(AppError::Path(format!(
            "无法访问目录 '{}': {}",
            path.display(),
            e
        ))),
    }
}

/// 对 LRA 结果文件进行排序
/// 
/// 读取结果文件，按照 LRA 值从高到低排序，然后重新写入文件。
/// 排序有助于用户快速识别动态范围最大和最小的音频文件。
/// 
/// # 参数
/// - `results_file_path` - 结果文件的路径
/// - `header_line` - 文件头部说明行
/// 
/// # 返回值
/// - `Ok(())` - 排序成功完成
/// - `Err(Box<dyn std::error::Error>)` - 文件操作或解析错误
pub fn sort_lra_results_file(
    results_file_path: &Path,
    header_line: &str,
) -> Result<(), Box<dyn std::error::Error>> {
    println!("\n正在排序结果文件: {}", results_file_path.display());
    let file = File::open(results_file_path)?;
    let reader = BufReader::new(file);
    let mut entries: Vec<(String, f64)> = Vec::new();
    let mut lines_iter = reader.lines();

    if lines_iter.next().is_none() {
        println!("结果文件为空或只有表头，无需排序。");
        let mut writer = BufWriter::new(File::create(results_file_path)?);
        writeln!(writer, "{header_line}")?;
        writer.flush()?;
        return Ok(());
    }

    for line_result in lines_iter {
        let line = line_result?;
        if line.trim().is_empty() {
            continue;
        }
        match line.rsplit_once(" - ") {
            Some((path_part, lra_str_part)) => match lra_str_part.trim().parse::<f64>() {
                Ok(lra_value) => entries.push((path_part.to_string(), lra_value)),
                Err(e) => eprintln!(
                    "排序时警告: 无法解析行 '{line}' 中的LRA值 '{lra_str_part}': {e}"
                ),
            },
            None => eprintln!("排序时警告: 行 '{line}' 格式不符合预期。将被忽略。"),
        }
    }

    entries.sort_unstable_by(|a, b| b.1.total_cmp(&a.1));

    let mut writer = BufWriter::new(File::create(results_file_path)?);
    writeln!(writer, "{header_line}")?;
    for (path_str, lra) in entries {
        writeln!(writer, "{path_str} - {lra:.1}")?;
    }
    writer.flush()?;
    Ok(())
}
