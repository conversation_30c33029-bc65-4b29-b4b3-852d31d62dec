//! 并行处理模块
//! 
//! 负责音频文件的并行处理和进度跟踪。

use std::path::PathBuf;
use std::sync::atomic::{AtomicUsize, Ordering};
use std::thread;

use rayon::prelude::*;

use crate::audio::calculate_lra_direct;
use crate::error::ProcessFileError;

/// 并行处理音频文件的 LRA 计算
/// 
/// 使用 Rayon 库进行并行处理，同时提供实时的进度反馈。
/// 
/// # 参数
/// - `files_to_process` - 要处理的文件列表，包含文件路径和显示路径
/// 
/// # 返回值
/// 返回处理结果的向量，每个元素要么是成功的 (显示路径, LRA值)，
/// 要么是失败的 ProcessFileError
pub fn process_files_parallel(
    files_to_process: Vec<(PathBuf, String)>,
) -> Vec<Result<(String, f64), ProcessFileError>> {
    let total_files = files_to_process.len();
    let processed_count = AtomicUsize::new(0);

    println!("开始多线程直接分析...");

    files_to_process
        .into_par_iter()
        .map(|(current_file_path, display_path_str)| {
            let current_processed_atomic = processed_count.fetch_add(1, Ordering::SeqCst) + 1;
            println!(
                "  [线程 {:?}] ({}/{}) 直接分析: {}",
                thread::current().id(),
                current_processed_atomic,
                total_files,
                display_path_str
            );

            // 直接调用 calculate_lra_direct，传入原始文件路径
            match calculate_lra_direct(&current_file_path) {
                Ok(lra) => {
                    println!(
                        "    [线程 {:?}] ({}/{}) 分析成功: {} LRA: {:.1} LU",
                        thread::current().id(),
                        current_processed_atomic,
                        total_files,
                        display_path_str,
                        lra
                    );
                    Ok((display_path_str, lra))
                }
                Err(e) => {
                    let err_msg = format!("分析失败: {e}");
                    Err(ProcessFileError {
                        file_path: display_path_str,
                        message: err_msg,
                    })
                }
            }
        })
        .collect()
}

/// 处理结果统计
#[derive(Debug)]
pub struct ProcessingStats {
    /// 成功处理的文件数量
    pub successful: usize,
    /// 失败的文件数量
    pub failed: usize,
    /// 错误信息列表
    pub error_messages: Vec<String>,
}

/// 分析处理结果并生成统计信息
/// 
/// # 参数
/// - `results` - 处理结果向量
/// 
/// # 返回值
/// 返回处理统计信息和成功的结果列表
pub fn analyze_results(
    results: Vec<Result<(String, f64), ProcessFileError>>,
) -> (ProcessingStats, Vec<(String, f64)>) {
    let mut successful_results = Vec::new();
    let mut error_messages = Vec::new();
    let mut successful_count = 0;
    let mut failed_count = 0;

    for result in results {
        match result {
            Ok((path_str, lra)) => {
                successful_results.push((path_str, lra));
                successful_count += 1;
            }
            Err(e) => {
                error_messages.push(format!("文件 '{}': {}", e.file_path, e.message));
                failed_count += 1;
            }
        }
    }

    let stats = ProcessingStats {
        successful: successful_count,
        failed: failed_count,
        error_messages,
    };

    (stats, successful_results)
}

/// 显示处理结果统计
/// 
/// # 参数
/// - `stats` - 处理统计信息
pub fn display_processing_stats(stats: &ProcessingStats) {
    println!("\n并行分析阶段完成。");
    println!("成功处理 {} 个文件。", stats.successful);
    
    if stats.failed > 0 {
        println!("{} 个文件处理失败。详情如下:", stats.failed);
        for err_msg in &stats.error_messages {
            eprintln!("  - {err_msg}");
        }
    }
}
