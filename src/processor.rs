//! 并行处理模块 (Parallel Processing Module)
//!
//! 本模块负责音频文件的并行处理和进度跟踪，是程序性能优化的核心。
//! 通过充分利用多核 CPU 资源，显著提升大批量音频文件的处理效率。
//!
//! ## 核心设计理念
//!
//! ### 数据并行 (Data Parallelism)
//! 使用 Rayon 库实现数据并行处理，将文件列表分割到多个线程中并行执行。
//! 这种方式相比传统的任务并行更加高效，因为：
//! - 自动负载均衡：Rayon 使用工作窃取算法
//! - 零开销抽象：编译时优化，运行时性能接近手写线程代码
//! - 内存安全：利用 Rust 的所有权系统避免数据竞争
//!
//! ### 错误隔离 (Error Isolation)
//! 单个文件的处理失败不会影响其他文件的处理，确保程序的健壮性。
//! 所有错误都被收集并在最后统一报告。
//!
//! ### 进度跟踪 (Progress Tracking)
//! 使用原子计数器实现线程安全的进度跟踪，为用户提供实时反馈。

use std::path::PathBuf;
use std::sync::atomic::{AtomicUsize, Ordering};
use std::thread;

use rayon::prelude::*;

use crate::audio::calculate_lra_direct;
use crate::error::ProcessFileError;

/// 并行处理音频文件的 LRA 计算 (Parallel LRA Calculation for Audio Files)
///
/// 这是程序的性能核心，使用 Rayon 库实现数据并行处理。
/// 将文件列表分发到多个线程中并行执行 LRA 计算，同时提供实时的进度反馈。
///
/// ## 并行处理策略
///
/// ### 工作分配
/// - 使用 Rayon 的 `par_iter()` 将文件列表转换为并行迭代器
/// - 自动利用所有可用的 CPU 核心
/// - 工作窃取算法确保负载均衡
///
/// ### 进度跟踪
/// - 使用原子计数器 `AtomicUsize` 跟踪已处理文件数量
/// - 每个线程处理文件前原子性地增加计数器
/// - 实时显示处理进度和线程信息
///
/// ### 错误处理
/// - 单个文件失败不影响其他文件处理
/// - 错误信息包含文件路径和详细错误描述
/// - 根据错误类型进行分类，便于后续分析
///
/// ## 性能特性
/// - **CPU 密集型优化**: 充分利用多核处理器
/// - **内存效率**: 流式处理，避免大量内存占用
/// - **I/O 优化**: 并行 I/O 操作，减少等待时间
///
/// # 参数
/// - `files_to_process` - 要处理的文件列表，每个元素包含：
///   - `PathBuf` - 文件的完整路径（用于实际处理）
///   - `String` - 显示路径（用于用户界面）
///
/// # 返回值
/// 返回处理结果的向量，每个元素为：
/// - `Ok((String, f64))` - 成功：(显示路径, LRA值)
/// - `Err(ProcessFileError)` - 失败：包含错误详情的结构体
///
/// # 线程安全性
/// - 使用原子操作进行计数，避免数据竞争
/// - 每个文件的处理完全独立，无共享状态
/// - 输出操作使用 println! 宏，内部有锁保护
pub fn process_files_parallel(
    files_to_process: Vec<(PathBuf, String)>,
) -> Vec<Result<(String, f64), ProcessFileError>> {
    let total_files = files_to_process.len();
    let processed_count = AtomicUsize::new(0);

    println!("开始多线程直接分析...");
    println!("总文件数: {}, 可用 CPU 核心数: {}", total_files, rayon::current_num_threads());

    // 使用 Rayon 的并行迭代器进行数据并行处理
    // into_par_iter() 将 Vec 转换为并行迭代器，自动分配到多个线程
    files_to_process
        .into_par_iter()
        .map(|(current_file_path, display_path_str)| {
            // 原子性地增加已处理计数，确保线程安全
            // fetch_add 返回增加前的值，所以需要 +1 得到当前处理的文件序号
            let current_processed_atomic = processed_count.fetch_add(1, Ordering::SeqCst) + 1;

            // 显示开始处理的信息，包含线程 ID 用于调试
            println!(
                "  [线程 {:?}] ({}/{}) 开始分析: {}",
                thread::current().id(),
                current_processed_atomic,
                total_files,
                display_path_str
            );

            // 执行实际的 LRA 计算
            let result = process_single_file(&current_file_path, &display_path_str);

            // 根据处理结果显示相应的信息
            match &result {
                Ok((_, lra)) => {
                    println!(
                        "    [线程 {:?}] ({}/{}) ✓ 分析成功: {} → LRA: {:.1} LU",
                        thread::current().id(),
                        current_processed_atomic,
                        total_files,
                        display_path_str,
                        lra
                    );
                }
                Err(error) => {
                    println!(
                        "    [线程 {:?}] ({}/{}) ✗ 分析失败: {} → {}",
                        thread::current().id(),
                        current_processed_atomic,
                        total_files,
                        display_path_str,
                        error.message
                    );
                }
            }

            result
        })
        .collect()  // 收集所有结果到 Vec 中
}

/// 处理单个音频文件 (Process Single Audio File)
///
/// 这个辅助函数封装了单个文件的处理逻辑，包括 LRA 计算和错误分类。
/// 分离这个逻辑可以提高代码的可读性和可测试性。
///
/// ## 错误分类策略
/// 根据错误信息的内容自动判断错误类型：
/// - FFmpeg 相关错误：包含 "ffmpeg" 或 "FFmpeg" 关键词
/// - LRA 解析错误：包含 "解析" 或 "LRA" 关键词
/// - 其他错误：未分类的错误类型
///
/// # 参数
/// - `file_path` - 文件的完整路径
/// - `display_path` - 用于显示的路径
///
/// # 返回值
/// - `Ok((String, f64))` - 成功：(显示路径, LRA值)
/// - `Err(ProcessFileError)` - 失败：分类后的错误信息
fn process_single_file(
    file_path: &Path,
    display_path: &str
) -> Result<(String, f64), ProcessFileError> {
    match calculate_lra_direct(file_path) {
        Ok(lra) => Ok((display_path.to_string(), lra)),
        Err(e) => {
            let err_msg = format!("分析失败: {e}");

            // 根据错误信息内容自动分类错误类型
            let error = if err_msg.contains("ffmpeg") || err_msg.contains("FFmpeg") {
                ProcessFileError::ffmpeg_error(display_path.to_string(), err_msg)
            } else if err_msg.contains("解析") || err_msg.contains("LRA") {
                ProcessFileError::lra_parsing_error(display_path.to_string(), err_msg)
            } else {
                ProcessFileError::new(
                    display_path.to_string(),
                    err_msg,
                    crate::error::FileErrorType::Other
                )
            };

            Err(error)
        }
    }
}

/// 处理结果统计
#[derive(Debug)]
pub struct ProcessingStats {
    /// 成功处理的文件数量
    pub successful: usize,
    /// 失败的文件数量
    pub failed: usize,
    /// 错误信息列表
    pub error_messages: Vec<String>,
}

/// 分析处理结果并生成统计信息
/// 
/// # 参数
/// - `results` - 处理结果向量
/// 
/// # 返回值
/// 返回处理统计信息和成功的结果列表
pub fn analyze_results(
    results: Vec<Result<(String, f64), ProcessFileError>>,
) -> (ProcessingStats, Vec<(String, f64)>) {
    let mut successful_results = Vec::new();
    let mut error_messages = Vec::new();
    let mut successful_count = 0;
    let mut failed_count = 0;

    for result in results {
        match result {
            Ok((path_str, lra)) => {
                successful_results.push((path_str, lra));
                successful_count += 1;
            }
            Err(e) => {
                error_messages.push(format!("文件 '{}': {}", e.file_path, e.message));
                failed_count += 1;
            }
        }
    }

    let stats = ProcessingStats {
        successful: successful_count,
        failed: failed_count,
        error_messages,
    };

    (stats, successful_results)
}

/// 显示处理结果统计
/// 
/// # 参数
/// - `stats` - 处理统计信息
pub fn display_processing_stats(stats: &ProcessingStats) {
    println!("\n并行分析阶段完成。");
    println!("成功处理 {} 个文件。", stats.successful);
    
    if stats.failed > 0 {
        println!("{} 个文件处理失败。详情如下:", stats.failed);
        for err_msg in &stats.error_messages {
            eprintln!("  - {err_msg}");
        }
    }
}
