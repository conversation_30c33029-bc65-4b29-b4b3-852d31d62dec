//! 音频处理模块
//! 
//! 包含音频文件扫描、LRA 计算等核心功能。

use std::path::{Path, PathBuf};
use std::process::Command;
use regex::Regex;
use walkdir::WalkDir;

use crate::error::AppError;

/// 支持的音频文件扩展名列表
/// 
/// 包含常见的音频格式，如无损格式（WAV、FLAC、AIFF、ALAC）
/// 和有损格式（MP3、AAC、OGG、Opus、WMA）
pub const SUPPORTED_EXTENSIONS: [&str; 10] = [
    "wav", "mp3", "m4a", "flac", "aac", "ogg", "opus", "wma", "aiff", "alac",
];

/// 扫描指定目录中的音频文件
/// 
/// 递归遍历目录，查找所有支持格式的音频文件。
/// 
/// # 参数
/// - `base_path` - 要扫描的根目录
/// - `exclude_file` - 要排除的文件（通常是结果文件）
/// 
/// # 返回值
/// 返回包含文件路径和显示路径的元组向量
pub fn scan_audio_files(
    base_path: &Path,
    exclude_file: Option<&Path>,
) -> Vec<(PathBuf, String)> {
    let mut files_to_process = Vec::new();
    
    for entry_result in WalkDir::new(base_path)
        .into_iter()
        .filter_map(Result::ok)
        .filter(|e| e.file_type().is_file())
    {
        let current_file_path = entry_result.path().to_path_buf();
        
        // 排除指定文件
        if let Some(exclude) = exclude_file {
            if current_file_path == exclude {
                continue;
            }
        }
        
        // 检查文件扩展名
        if let Some(extension) = current_file_path
            .extension()
            .and_then(|ext| ext.to_str())
            .map(|s| s.to_lowercase())
        {
            if SUPPORTED_EXTENSIONS.contains(&extension.as_str()) {
                let display_path_str = current_file_path
                    .strip_prefix(base_path)
                    .unwrap_or(&current_file_path)
                    .to_string_lossy()
                    .into_owned();
                files_to_process.push((current_file_path, display_path_str));
            }
        }
    }
    
    files_to_process
}

/// 直接计算音频文件的 LRA 值
/// 
/// 使用 FFmpeg 的 ebur128 滤波器直接分析音频文件，
/// 无需进行格式转换，支持多种音频格式。
/// 
/// # 参数
/// - `audio_file_path` - 要分析的音频文件路径
/// 
/// # 返回值
/// - `Ok(f64)` - 计算得到的 LRA 值（单位：LU）
/// - `Err(Box<dyn std::error::Error + Send + Sync>)` - 分析过程中的错误
/// 
/// # 实现细节
/// 调用 FFmpeg 命令：`ffmpeg -i <file> -filter_complex ebur128 -f null -`
/// 从 stderr 输出中解析 LRA 值
pub fn calculate_lra_direct(
    audio_file_path: &Path,
) -> Result<f64, Box<dyn std::error::Error + Send + Sync>> {
    let output = Command::new("ffmpeg")
        .arg("-i")
        .arg(audio_file_path)
        .arg("-filter_complex")
        .arg("ebur128")
        .arg("-f")
        .arg("null")
        .arg("-hide_banner")
        .arg("-loglevel")
        .arg("info") // ebur128 输出在 info 级别
        .arg("-")
        .output()?;

    let stderr_output = String::from_utf8_lossy(&output.stderr);

    let re = Regex::new(r"LRA:\s*([\d\.-]+)\s*LU")?;
    if let Some(caps) = re.captures_iter(&stderr_output).last() {
        if let Some(lra_str) = caps.get(1) {
            return lra_str.as_str().parse::<f64>().map_err(|e| {
                format!(
                    "解析LRA值 '{}' (来自文件 {}) 失败: {}",
                    lra_str.as_str(),
                    audio_file_path.display(),
                    e
                )
                .into()
            });
        }
    }
    Err(format!(
        "无法从 ffmpeg 输出中为文件 {} 解析 LRA 值. stderr: {}",
        audio_file_path.display(),
        stderr_output.trim()
    )
    .into())
}

/// 验证 FFmpeg 是否可用
/// 
/// 检查系统中是否安装了 FFmpeg 并且可以正常调用
pub fn check_ffmpeg_availability() -> Result<(), AppError> {
    match Command::new("ffmpeg").arg("-version").output() {
        Ok(output) => {
            if output.status.success() {
                println!("✓ FFmpeg 检测成功");
                Ok(())
            } else {
                Err(AppError::Ffmpeg(
                    "FFmpeg 存在但无法正常运行".to_string(),
                ))
            }
        }
        Err(_) => Err(AppError::Ffmpeg(
            "未找到 FFmpeg，请确保已安装并添加到 PATH 环境变量中".to_string(),
        )),
    }
}
