# 用户手册 (User Guide)

本手册详细介绍了 LRA 音频响度范围计算器的所有功能和使用方法。

## 📖 目录

1. [程序概述](#程序概述)
2. [基本使用流程](#基本使用流程)
3. [支持的音频格式](#支持的音频格式)
4. [输出结果解读](#输出结果解读)
5. [高级功能](#高级功能)
6. [性能优化建议](#性能优化建议)
7. [使用场景示例](#使用场景示例)

## 🎯 程序概述

### 什么是 LRA？

LRA（Loudness Range，响度范围）是根据 EBU R128 标准定义的音频测量指标，用于量化音频内容的动态范围。它以 LU（Loudness Units，响度单位）为单位，反映音频中最安静和最响亮部分之间的差异。

### 程序功能特点

- **批量处理**: 递归扫描目录，一次性处理大量音频文件
- **多线程并行**: 充分利用多核 CPU，显著提升处理速度
- **格式广泛**: 支持 10+ 种主流音频格式
- **精确测量**: 基于 FFmpeg 的 ebur128 滤波器，符合国际标准
- **中文界面**: 完全中文化的用户界面和输出
- **结果排序**: 自动按 LRA 值排序，便于分析

## 🚀 基本使用流程

### 第一步：启动程序

```bash
# 运行发布版本（推荐）
./target/release/LRA-Calculator-Rust

# 或运行开发版本
cargo run
```

### 第二步：程序初始化

程序启动后会显示欢迎信息和系统检查：

```
欢迎使用音频 LRA 计算器（高性能版 - 直接分析）！
当前时间: 2025-08-07 14:30:00
✓ FFmpeg 检测成功
请输入要递归处理的音乐顶层文件夹路径: 
```

### 第三步：输入目标路径

输入包含音频文件的目录路径：

```bash
# 示例路径
/Users/<USER>/Music/MyCollection
/home/<USER>/audio-files
C:\Users\<USER>\Music
```

**路径输入提示**：
- 支持绝对路径和相对路径
- 路径中可以包含空格（会自动处理）
- 支持 Unicode 字符（中文路径名）
- 程序会递归扫描所有子目录

### 第四步：文件扫描

程序会递归扫描指定目录：

```
正在递归扫描文件夹: /Users/<USER>/Music/MyCollection
扫描完成，找到 150 个音频文件待处理。
```

### 第五步：并行处理

程序使用多线程并行处理文件：

```
开始多线程直接分析...
  [线程 ThreadId(2)] (1/150) 直接分析: album1/track01.mp3
  [线程 ThreadId(3)] (2/150) 直接分析: album1/track02.flac
  [线程 ThreadId(4)] (3/150) 直接分析: album2/song01.wav
    [线程 ThreadId(2)] (1/150) 分析成功: album1/track01.mp3 LRA: 12.3 LU
    [线程 ThreadId(3)] (2/150) 分析成功: album1/track02.flac LRA: 15.7 LU
```

### 第六步：结果输出

处理完成后显示统计信息：

```
处理完成！
成功处理: 148 个文件
处理失败: 2 个文件

失败文件详情:
文件 'corrupted/bad_file.mp3': 分析失败: 无法从 ffmpeg 输出中解析 LRA 值

结果写入完成。
结果文件 /Users/<USER>/Music/MyCollection/lra_results.txt 已成功排序。
```

## 🎵 支持的音频格式

| 格式类别 | 扩展名 | 格式说明 | 典型用途 |
|----------|--------|----------|----------|
| **无损格式** | .wav | 未压缩音频，质量最高 | 专业录音、母带 |
| | .flac | 无损压缩，文件较小 | 高品质音乐收藏 |
| | .aiff | 苹果无损格式 | Mac 平台录音 |
| | .alac | 苹果无损压缩 | iTunes 高品质音乐 |
| **有损格式** | .mp3 | 最常见的压缩格式 | 日常音乐播放 |
| | .aac/.m4a | 高效音频编码 | 流媒体、移动设备 |
| | .ogg | 开源音频格式 | 游戏、开源应用 |
| | .opus | 现代低延迟编解码器 | 语音通话、流媒体 |
| | .wma | Windows 媒体音频 | Windows 平台 |

### 格式选择建议

- **精确分析**: 优先使用无损格式（WAV、FLAC）
- **批量处理**: 有损格式也能提供可靠的 LRA 测量
- **混合处理**: 程序可同时处理多种格式

## 📊 输出结果解读

### 结果文件格式

程序在指定目录生成 `lra_results.txt` 文件：

```
文件路径 (相对) - LRA 数值 (LU)
classical/beethoven_symphony_9.wav - 18.5
jazz/miles_davis_kind_of_blue.flac - 14.2
rock/led_zeppelin_stairway.mp3 - 12.8
pop/taylor_swift_song.m4a - 8.1
podcast/tech_talk_episode_01.mp3 - 5.5
audiobook/chapter_01.mp3 - 3.2
```

### LRA 值含义解读

#### 高动态范围 (15+ LU)
- **特征**: 音量变化丰富，从很安静到很响亮
- **典型内容**: 古典音乐、交响乐、爵士乐、现场录音
- **听感**: 层次分明，情感表达丰富

#### 中等动态范围 (8-15 LU)
- **特征**: 适中的音量变化，平衡的动态
- **典型内容**: 摇滚音乐、民谣、部分流行音乐
- **听感**: 既有变化又不过于极端

#### 低动态范围 (0-8 LU)
- **特征**: 音量相对稳定，变化较小
- **典型内容**: 现代流行音乐、播客、有声书、广播
- **听感**: 音量一致，适合背景播放

#### 极低动态范围 (0-3 LU)
- **特征**: 音量几乎无变化，高度压缩
- **典型内容**: 重度压缩的流行音乐、广告、某些电子音乐
- **听感**: 可能存在"响度战争"问题

### 结果分析技巧

1. **按类型分组**: 比较同类型音乐的 LRA 差异
2. **历史对比**: 分析不同年代音乐的动态范围变化
3. **格式影响**: 对比同一内容不同格式的 LRA 值
4. **质量评估**: 识别过度压缩的音频内容

## ⚙️ 高级功能

### 批量处理策略

#### 大型音乐库处理
```bash
# 处理整个音乐库
./target/release/LRA-Calculator-Rust
# 输入: /Users/<USER>/Music

# 分批处理（推荐大型库）
./target/release/LRA-Calculator-Rust
# 输入: /Users/<USER>/Music/Classical
# 然后处理其他子目录
```

#### 网络存储处理
```bash
# 挂载网络驱动器后处理
mount -t cifs //server/music /mnt/music
./target/release/LRA-Calculator-Rust
# 输入: /mnt/music
```

### 结果文件管理

#### 合并多个结果文件
```bash
# 合并多个 lra_results.txt 文件
cat */lra_results.txt | grep -v "文件路径" | sort -k3 -nr > combined_results.txt
```

#### 结果数据分析
```bash
# 统计不同 LRA 范围的文件数量
awk -F' - ' 'NR>1 {lra=$2; if(lra>=15) high++; else if(lra>=8) mid++; else low++} END {print "高动态:", high, "中动态:", mid, "低动态:", low}' lra_results.txt
```

## 🚀 性能优化建议

### 硬件优化

1. **CPU**: 多核处理器能显著提升并行处理速度
2. **内存**: 8GB+ RAM 推荐，处理大量文件时
3. **存储**: SSD 硬盘提供更好的 I/O 性能
4. **网络**: 处理网络存储时确保稳定连接

### 软件优化

1. **关闭不必要程序**: 释放 CPU 和内存资源
2. **使用发布版本**: `cargo build --release` 提供最佳性能
3. **批量处理**: 避免频繁启动程序

### 系统配置优化

```bash
# Linux: 增加文件描述符限制
ulimit -n 65536

# 设置进程优先级
nice -n -10 ./target/release/LRA-Calculator-Rust
```

## 📝 使用场景示例

### 场景一：音乐收藏分析
```
目标: 分析个人音乐收藏的动态范围分布
步骤:
1. 处理整个音乐库
2. 按艺术家/专辑分类分析
3. 识别动态范围异常的文件
4. 优化音乐库质量
```

### 场景二：音频制作质量控制
```
目标: 确保音频制作符合动态范围标准
步骤:
1. 处理制作完成的音频文件
2. 检查 LRA 值是否符合目标范围
3. 识别需要重新处理的文件
4. 生成质量报告
```

### 场景三：音频格式转换验证
```
目标: 验证格式转换对动态范围的影响
步骤:
1. 处理原始无损文件
2. 转换为有损格式
3. 再次测量 LRA 值
4. 比较转换前后的差异
```

### 场景四：播客/有声书质量检查
```
目标: 确保语音内容的一致性
步骤:
1. 处理所有音频章节
2. 检查 LRA 值的一致性
3. 识别音量异常的章节
4. 标准化处理建议
```

## 🔍 故障排除

### 常见问题

1. **文件跳过**: 检查文件格式和权限
2. **处理缓慢**: 检查硬件资源和网络连接
3. **结果异常**: 验证音频文件完整性
4. **内存不足**: 减少并发处理或增加内存

### 错误信息解读

- `FFmpeg 错误`: 检查 FFmpeg 安装和音频文件格式
- `权限被拒绝`: 确保对目录有读写权限
- `文件不存在`: 检查路径输入是否正确
- `解析失败`: 音频文件可能损坏或格式不支持

## 📚 相关文档

- 📖 [快速开始指南](./quick-start.md) - 5分钟上手
- 🔧 [安装指南](./installation.md) - 详细安装说明
- 🏗️ [架构设计](./architecture.md) - 技术实现细节
- ❓ [常见问题](./faq.md) - 问题解答

---

*本手册持续更新中。如有疑问或建议，请创建 GitHub Issue。*
